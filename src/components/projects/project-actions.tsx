"use client";

import { useMutation } from "@tanstack/react-query";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import type { Job } from "@/db/schema";

interface ProjectActionsProps {
  job: Job;
  userRole: string;
  layout?: "horizontal" | "vertical";
}

export function ProjectActions({
  job,
  userRole,
  layout = "horizontal",
}: ProjectActionsProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const [isPublishing, setIsPublishing] = useState(false);

  const publishJob = useMutation(
    trpc.jobs.publish.mutationOptions({
      onSuccess: () => {
        toast.success("Project published successfully");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error publishing project: ${error.message}`);
      },
      onSettled: () => {
        setIsPublishing(false);
      },
    }),
  );

  const handlePublish = () => {
    setIsPublishing(true);
    publishJob.mutate({ id: job.id });
  };

  // Define action buttons based on user role and job status (matching original implementation)
  const isHomeowner = userRole === "homeowner";
  const isProfessional = userRole === "contractor";
  const isDraft = job.status === "DRAFT";
  const isPublished = job.status === "PUBLISHED";
  const isAwarded = job.status === "AWARDED";
  const isCompleted = job.status === "COMPLETED";
  const isQuickHire = job.jobType === "QUICK_HIRE";

  const containerClass = layout === "vertical" ? "space-y-2" : "flex gap-2";
  const buttonClass = layout === "vertical" ? "w-full" : "";

  return (
    <div className={containerClass}>
      {/* Publish button for draft projects (homeowners only) */}
      {!isProfessional && isDraft && (
        <Button
          variant="default"
          onClick={handlePublish}
          disabled={isPublishing}
          className={`${buttonClass} bg-green-600 hover:bg-green-700`}
        >
          {isPublishing ? "Publishing..." : "Publish Project"}
        </Button>
      )}

      {/* Bid button for professionals on published projects */}
      {isProfessional && isPublished && (
        <Button variant="tc_blue" asChild className={buttonClass}>
          <Link href={`/projects/${job.id}/bid`}>Bid Now</Link>
        </Button>
      )}

      {/* Edit and task actions for homeowners on editable projects */}
      {!isProfessional && !(isAwarded || isCompleted) && (
        <>
          <Button variant="tc_orange" asChild className={buttonClass}>
            <Link href={`/projects/${job.id}/edit`}>Edit Project</Link>
          </Button>
          <Button variant="outline" asChild className={buttonClass}>
            <Link href={`/projects/${job.id}/edit`}>Add Task</Link>
          </Button>
        </>
      )}

      {/* Completion actions for awarded projects */}
      {isHomeowner && isAwarded && (
        <>
          {!isQuickHire && (
            <Button
              variant="default"
              className={`${buttonClass} bg-green-600 hover:bg-green-700`}
              asChild
            >
              <Link href={`/projects/${job.id}/complete`}>
                {job.homeownerCompleted
                  ? "Completion Pending"
                  : "Mark Complete"}
              </Link>
            </Button>
          )}
          {isQuickHire && (
            <Button variant="default" asChild className={buttonClass}>
              <Link href={`/projects/${job.id}/schedule`}>Schedule Job</Link>
            </Button>
          )}
        </>
      )}

      {isProfessional && isAwarded && (
        <>
          {!isQuickHire && (
            <Button
              variant="default"
              className={`${buttonClass} bg-green-600 hover:bg-green-700`}
              asChild
            >
              <Link href={`/projects/${job.id}/complete`}>
                {job.contractorCompleted
                  ? "Completion Pending"
                  : "Mark Complete"}
              </Link>
            </Button>
          )}
          {isQuickHire && (
            <Button variant="default" asChild className={buttonClass}>
              <Link href={`/projects/${job.id}/schedule`}>Schedule Job</Link>
            </Button>
          )}
        </>
      )}

      {/* Review actions for completed projects */}
      {isCompleted && (
        <Button variant="default" asChild className={buttonClass}>
          <Link href={`/projects/${job.id}/review`}>Leave Review</Link>
        </Button>
      )}

      {/* Cancel project for draft projects */}
      {isDraft && !isProfessional && (
        <Button variant="outline" asChild className={buttonClass}>
          <Link href={`/projects/${job.id}/cancel`}>Cancel Project</Link>
        </Button>
      )}

      {/* Back to dashboard - only show in vertical layout */}
      {layout === "vertical" && (
        <Button variant="outline" asChild className={buttonClass}>
          <Link href="/projects">Back to Dashboard</Link>
        </Button>
      )}
    </div>
  );
}
