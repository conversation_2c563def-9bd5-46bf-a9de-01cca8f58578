import { Suspense } from "react";
import { PropertyGridSkeleton } from "@/components/loading-states";
import { PageLayout } from "@/components/page-layout";
import { PropertiesContent } from "@/components/properties/properties-content";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";

export default async function PropertiesPage() {
  const queryClient = getQueryClient();

  // Prefetch properties data on the server
  await queryClient.prefetchQuery(trpc.properties.list.queryOptions());

  return (
    <PageLayout title="Properties">
      <HydrateClient>
        <Suspense fallback={<PropertyGridSkeleton />}>
          <PropertiesContent />
        </Suspense>
      </HydrateClient>
    </PageLayout>
  );
}
