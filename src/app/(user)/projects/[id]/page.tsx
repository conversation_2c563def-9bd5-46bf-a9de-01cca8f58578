import { headers } from "next/headers";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";
import { PageLayout } from "@/components/page-layout";
import { ProjectActions } from "@/components/projects/project-actions";
import { ProjectDetailContent } from "@/components/projects/project-detail-content";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";
import { Skeleton } from "@/components/ui/skeleton";
import { auth } from "@/lib/auth";

function ProjectDetailSkeleton() {
  return (
    <div className="p-8">
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Skeleton className="h-[400px] w-full" />
          <Skeleton className="mt-6 h-[200px] w-full" />
          <Skeleton className="mt-6 h-[300px] w-full" />
        </div>
        <div>
          <Skeleton className="h-[500px] w-full" />
        </div>
      </div>
    </div>
  );
}

type Props = {
  params: Promise<{ id: string }>;
};

export default async function JobDetailPage({ params }: Props) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  const queryClient = getQueryClient();

  // Fetch the job data
  const job = await queryClient.fetchQuery(
    trpc.jobs.getById.queryOptions({
      id,
    }),
  );

  if (!job) {
    notFound();
  }

  // Prefetch related data
  await Promise.all([
    // Prefetch bids if it's a standard job
    job.jobType !== "QUICK_HIRE" && job.bids?.length
      ? Promise.all(
          job.bids.map((bid) =>
            queryClient.prefetchQuery(
              trpc.bids.getById.queryOptions({ id: bid.id }),
            ),
          ),
        )
      : Promise.resolve(),

    // Prefetch crew data if there's an accepted bid
    (() => {
      const acceptedBid = job.bids?.find((bid) => bid.status === "ACCEPTED");
      return acceptedBid
        ? queryClient.prefetchQuery(
            trpc.contractor.getCrewMembers.queryOptions({
              organizationId: acceptedBid.organizationId,
            }),
          )
        : Promise.resolve();
    })(),
  ]);

  const actions = <ProjectActions job={job} userRole={session.user.role} />;

  return (
    <PageLayout title={`Project: ${job.name}`} actions={actions}>
      <HydrateClient>
        <Suspense fallback={<ProjectDetailSkeleton />}>
          <ProjectDetailContent
            jobId={id}
            userId={session.user.id}
            userRole={session.user.role}
          />
        </Suspense>
      </HydrateClient>
    </PageLayout>
  );
}
