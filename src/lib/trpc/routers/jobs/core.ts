import { TRPCError } from "@trpc/server";
import { asc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { job, jobImage } from "@/db/schema";
import { protectedProcedure } from "@/lib/trpc/procedures";
import { checkJobOwnership, requireAuth } from "@/lib/trpc/utils/permissions";
import { batchLoadJobsWithRelations } from "@/lib/trpc/utils/query-optimizations";
import {
  withQueryCache,
  withQueryPerformanceMonitoring,
} from "@/lib/trpc/utils/query-performance";

export const coreJobsRouter = {
  list: protectedProcedure
    .input(z.object({ limit: z.number().optional() }).optional())
    .query(async ({ input }) => {
      const result = await db.query.job.findMany({
        orderBy: [asc(job.name)],
        with: {
          property: true,
          bids: {
            with: {
              organization: true,
            },
          },
        },
        limit: input?.limit,
      });

      // Add bidsCount manually to avoid Drizzle SQL generation bug
      return result.map((job) => ({
        ...job,
        bidsCount: job.bids?.length ?? 0,
      }));
    }),
  getById: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        includeReviews: z.boolean().default(false),
        includeSchedules: z.boolean().default(false),
      }),
    )
    .query(async ({ input, ctx }) => {
      const { id, includeReviews, includeSchedules } = input;

      return withQueryPerformanceMonitoring("jobs.getById", async () => {
        const cacheKey = `job:${id}:${includeReviews}:${includeSchedules}`;

        return withQueryCache(
          cacheKey,
          async () => {
            const [jobData] = await batchLoadJobsWithRelations(db, [id], {
              includeBids: true,
              includeProperty: true,
              includeReviews,
              includeSchedules,
            });

            if (!jobData) {
              throw new TRPCError({
                code: "NOT_FOUND",
                message: "Job not found",
              });
            }

            // Check permissions
            const hasAccess =
              jobData.property.userId === ctx.userId || // Property owner
              // biome-ignore lint/suspicious/noExplicitAny: Temporary fix for PostGIS query restructure
              jobData.bids.some((bid: any) => {
                // Type assertion: we know the organization includes memberships from batchLoadJobsWithRelations
                const bidWithOrg = bid as typeof bid & {
                  organization?: {
                    memberships?: { userId: string }[];
                  };
                };
                return bidWithOrg.organization?.memberships?.some(
                  // biome-ignore lint/suspicious/noExplicitAny: Temporary fix for PostGIS query restructure
                  (m: any) => m.userId === ctx.userId,
                );
              }); // Bidder

            if (!hasAccess) {
              throw new TRPCError({
                code: "FORBIDDEN",
                message: "You don't have access to this job",
              });
            }

            return jobData;
          },
          120000, // 2 minutes cache
        );
      });
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        propertyId: z.string(),
        images: z
          .object({
            url: z.string(),
            description: z.string().optional().nullable(),
          })
          .array()
          .optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to update this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to update this job");

      // First, delete existing images if we're updating them
      if (input.images) {
        await db.delete(jobImage).where(eq(jobImage.jobId, input.id));
      }

      // Update the job
      const [result] = await db
        .update(job)
        .set({
          name: input.name,
          propertyId: input.propertyId,
        })
        .where(eq(job.id, input.id))
        .returning();

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Add images if they exist
      if (input.images && input.images.length > 0) {
        await db.insert(jobImage).values(
          input.images.map((image) => ({
            jobId: input.id,
            url: image.url,
            description: image.description,
          })),
        );
      }

      return result;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to delete this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to delete this job");

      // Get the job before deletion
      const result = await db.query.job.findFirst({
        where: eq(job.id, input.id),
      });

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Delete the job
      await db.delete(job).where(eq(job.id, input.id));

      return result;
    }),

  publish: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to publish this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to publish this job");

      // Update the job status to PUBLISHED
      const [result] = await db
        .update(job)
        .set({ status: "PUBLISHED" })
        .where(eq(job.id, input.id))
        .returning();

      return result;
    }),

  cancel: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to cancel this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to cancel this job");

      // Check if job is in a state that can be cancelled
      const jobData = await db.query.job.findFirst({
        where: eq(job.id, input.id),
      });

      if (!jobData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Only allow cancellation of DRAFT or PUBLISHED jobs
      if (jobData.status !== "DRAFT" && jobData.status !== "PUBLISHED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Only draft or published projects can be cancelled",
        });
      }

      // Update the job status to CANCELLED
      const [result] = await db
        .update(job)
        .set({ status: "CANCELED" })
        .where(eq(job.id, input.id))
        .returning();

      return result;
    }),
};
